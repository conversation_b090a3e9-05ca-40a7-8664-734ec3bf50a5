import React, { useCallback, useMemo, useState } from 'react';
import { FormikProps } from 'formik';
import { map, reduce, sumBy } from 'lodash';
import { useMutation } from 'react-apollo';

import { ICreateOrUpdateStudentAssessmentParams } from './RubricForm';
import updateAssessmentNumGql from '../../../data/updateStudentAssessmentNum.graphql';
import useT from '../../../../../../../../common/components/utils/Translations/useT';
import ILearningSpaceLPTask from '../../../../../../../../common/abstract/MySpace/MyLearningSpaceStaff/ILearningSpaceLPTask';
import EntityForm from '../../../../../../../../common/components/containers/EntityForm';
import RubricTable from '../../../../../../../../common/components/forms/LearningTaskPlanTree/TaskEditForm/Tabs/Assessment/Rubric/RubricTable';
import { isRubricMatrix } from '../../../../../../../../common/components/forms/LearningTaskPlanTree/TaskEditForm/Tabs/Assessment/Rubric/RubricTable/RubricTable';
import ILpTaskRubricMatrixXStudent from '../../../../../../../../common/abstract/OrganisationGroup/ProgramGroups/ILpTaskRubricMatrixXStudent';
import LoadingMask from '../../../../../../../../common/components/utils/LoadingMask';
import ITaskStudentAssign from '../../../../../../../../common/abstract/MySpace/MyLearningSpaceStaff/Submissions/ITaskStudentAssign';
import Notifications from '../../../../../../../../common/utils/Notifications/Notifications';
import {
  Percent,
  PercentRange,
} from '../../../../../../../../model/RubricCalculationMethods';
import { HUNDRED } from '../../../../../../../../common/const';

const RubricTabFooter: React.FC<{
  assessmentFormRef: FormikProps<
    Partial<ICreateOrUpdateStudentAssessmentParams>
  >;
  taskStudentAssign: ITaskStudentAssign;
  learningSpaceLPTask: ILearningSpaceLPTask;
}> = ({ assessmentFormRef, learningSpaceLPTask, taskStudentAssign }) => {
  const t = useT();

  const { values, setFieldValue, validateForm } = assessmentFormRef;
  const {
    id: taskId,
    classId,
    hasRubric,
    rubric,
    assessmentType,
    maximum,
  } = learningSpaceLPTask;

  const { id: taskStudentAssignId, taskRubricMatricesXStudents } = values || {};

  const entity = useMemo(
    () => ({
      id: taskStudentAssignId,
      rubric: {
        ...rubric,
        taskRubricMatricesXStudents,
      },
    }),
    [rubric, taskRubricMatricesXStudents, taskStudentAssignId],
  );

  const { person } = taskStudentAssign;
  const [updateAssessmentNum, updateAssessmentNumGqlRes] = useMutation(
    updateAssessmentNumGql,
  );

  const isLoading = updateAssessmentNumGqlRes.loading;

  const [
    currTaskRubricMatricesXStudents,
    setCurrTaskRubricMatricesXStudents,
  ] = useState(entity.rubric.taskRubricMatricesXStudents);

  const handleSubmit = useCallback(
    async (values, isCopied) => {
      console.log('values', values);
      console.log('isCopied', isCopied);
      const taskRubricMatricesXStudents = map(values, node => ({
        ...node,
        point: parseFloat(node.point) || 0,
      }));
      const oldStudentTaskAssignId =
        currTaskRubricMatricesXStudents?.length &&
        currTaskRubricMatricesXStudents[0].taskStudentAssignId;
      const newStudentTaskAssignId =
        taskRubricMatricesXStudents?.length &&
        taskRubricMatricesXStudents[0].taskStudentAssignId;
      const oldPoints = sumBy(currTaskRubricMatricesXStudents, 'point');
      const numResult = sumBy(taskRubricMatricesXStudents, 'point');

      if (
        oldStudentTaskAssignId !== newStudentTaskAssignId ||
        !taskRubricMatricesXStudents.length ||
        (oldPoints === numResult && !isCopied)
      )
        return;

      let mark = numResult;
      if (
        (maximum && rubric?.calculationMethod === Percent.value) ||
        (maximum && rubric?.calculationMethod === PercentRange.value)
      ) {
        mark = (numResult / HUNDRED) * maximum;
      }
      const variables: {
        taskStudentAssignId?: number;
        params: Partial<ICreateOrUpdateStudentAssessmentParams>;
      } = {
        params: {
          classId,
          taskStudentAssignId,
          personId: person?.id,
          taskId,
          ...(isCopied && { numResult: Math.round(mark) }),
          taskRubricMatricesXStudents,
        },
        taskStudentAssignId,
      };

      setCurrTaskRubricMatricesXStudents(taskRubricMatricesXStudents);
      await updateAssessmentNum({
        variables,
      });
      Notifications.success(t('Updated Successfully'), '', t);
    },
    [
      maximum,
      rubric,
      classId,
      taskStudentAssignId,
      taskId,
      person,
      updateAssessmentNum,
      currTaskRubricMatricesXStudents,
      t,
    ],
  );

  const updateSelectedMatrices = useCallback(
    async (rubricTreeState, isCopied = false) => {
      console.log('updateSelectedMatrices');
      const cooked = cookTaskRubricMatricesXStudents(
        taskStudentAssignId,
        rubricTreeState,
      );
      await handleSubmit(cooked, isCopied);
      return cooked;
    },
    [taskStudentAssignId, handleSubmit],
  );

  const handleRubricSubmit = useCallback(
    ({ rubricTreeState }) => {
      console.log('handleRubricSubmit');
      updateSelectedMatrices(rubricTreeState);
    },
    [updateSelectedMatrices],
  );

  const handleRubricSubmitLeave = useCallback(
    ({ rubricTreeState }) => {
      updateSelectedMatrices(rubricTreeState, true);
    },
    [updateSelectedMatrices],
  );
  const [isCopying, setIsCopying] = useState<boolean>(false);

  const handleCopyToMarkAssigned = useCallback(
    async (rubricTreeState, value) => {
      let mark = value;
      if (
        (maximum && rubric?.calculationMethod === Percent.value) ||
        (maximum && rubric?.calculationMethod === PercentRange.value)
      ) {
        mark = (value / HUNDRED) * maximum;
      }
      setIsCopying(true);
      await updateSelectedMatrices(rubricTreeState, true);
      setIsCopying(false);
      return validateForm({ ...values, numResult: Math.round(mark) });
    },
    [updateSelectedMatrices, validateForm, values, maximum, rubric],
  );

  if (hasRubric) {
    return (
      <EntityForm
        isAlwaysEditable
        entity={entity}
        entityName="assessmentRubric"
        hasCreateMessage={false}
        hasUpdateMessage={false}
        submitSectionRenderer={renderNull}
        onLeave={handleRubricSubmitLeave}
        onSubmit={handleRubricSubmit}
      >
        <LoadingMask active={isCopying}>
          <RubricTable
            isAssessmentMode
            assessmentType={assessmentType}
            onCopyToMarkAssigned={handleCopyToMarkAssigned}
          />
        </LoadingMask>
      </EntityForm>
    );
  }
  return <></>;
};

export default RubricTabFooter;

const cookTaskRubricMatricesXStudents = (
  taskStudentAssignId,
  rubricTreeState,
) => {
  if (!rubricTreeState) {
    return null;
  }
  const { models } = rubricTreeState;

  return reduce(
    models,
    (acc, model) => {
      if (isRubricMatrix(model) && model.point) {
        acc.push({
          taskStudentAssignId,
          lpTaskRubricMatrixId: model.id,
          point: model.point,
        });
      }
      return acc;
    },
    [] as Partial<ILpTaskRubricMatrixXStudent>[],
  );
};
const renderNull = () => null;
