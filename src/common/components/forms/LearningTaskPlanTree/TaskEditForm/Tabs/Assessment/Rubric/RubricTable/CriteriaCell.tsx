import React, { useCallback, useContext } from 'react';
import classnames from 'classnames';
import { map, has } from 'lodash';

import styles from './RubricTable.scss';
import useT from '../../../../../../../utils/Translations/useT';
import TextAreaField from '../../../../../../../containers/EntityForm/fields/TextAreaField';
import CriteriaCellPoints from './CriteriaCellPoints';
import { MAX_CRITERIA_TEXT_LENGTH } from '../const';
import { isRubricMatrix, RubricTableContext } from './RubricTable';
import useEntityFormContext from '../../../../../../../containers/EntityForm/internal/useEntityFormContext';
import { useDependsOnFields } from '../../../../../../../containers/EntityForm';

export interface ICriteriaCell {
  id: string;
  prefix?: string;
}

const CriteriaCell: React.FC<ICriteriaCell> = ({ id, prefix = 'rubric' }) => {
  const t = useT();

  const {
    isAssessmentMode,
    tree,
    calculationMethod,
    isViewMode,
    isFullyAssessed,
  } = useContext(RubricTableContext);
  const { setFieldValue, values } = useEntityFormContext();

  const {
    point = values.rubric.taskRubricMatricesXStudents?.find(
      x => x.lpTaskRubricMatrixId === Number(id.split('-')[1]),
    )?.point,
  } = useDependsOnFields<{
    point: string;
  }>({
    point: `${prefix}.${id}.point`,
  });

  const setSelected = useCallback(
    e => {
      e.preventDefault();
      console.log('selected')
      updateSelectedMatrix({
        tree,
        setFieldValue,
        prefix,
        id,
        calculationMethod,
        values,
      });
    },
    [calculationMethod, setFieldValue, prefix, id, tree, values],
  );

  return (
    <td
      className={classnames('rubric-criteria', styles.rubricCriteria, {
        [styles.assessmentMode]: isAssessmentMode && !isViewMode,
        [styles.selectedCriteria]:
          isAssessmentMode &&
          (!isViewMode || isFullyAssessed) &&
          point !== undefined &&
          !!String(point).length,
      })}
      onClick={isAssessmentMode && !isViewMode ? setSelected : undefined}
    >
      <div className="mb-20">
        <TextAreaField
          autoHeight
          noLabel
          className={classnames('text-italic', styles.rubricTextArea)}
          columns={0}
          hasCharactersCounter={false}
          isReadOnly={isAssessmentMode || isViewMode}
          maxLength={MAX_CRITERIA_TEXT_LENGTH}
          name={`${prefix}.${id}.criteria`}
          placeholder={t('criteria')}
          viewClassName="no-border pl-5 pr-5 text-italic word-break-break-word"
        />
      </div>

      <CriteriaCellPoints id={id} prefix={prefix} />
    </td>
  );
};

export default CriteriaCell;

export const updateSelectedMatrix = ({
  tree,
  setFieldValue,
  prefix,
  id,
  calculationMethod,
  values,
}) => {
  const matrix = tree.models[id];
  const { lpTaskRubricSkillId } = matrix;
  map(tree.models, (model, id2) => {
    if (
      isRubricMatrix(model) &&
      model.lpTaskRubricSkillId === lpTaskRubricSkillId &&
      id !== id2
    ) {
      const currPoint = values.rubricTreeState.models[id];
      if (!has(currPoint, 'point') || currPoint.point === '') {
        setFieldValue(`${prefix}.${id}.point`, '0');
      }
      setFieldValue(`${prefix}.${id2}.point`, '');
    }
  });
};
